<?php

namespace app\jobs;

use app\components\collection\Collection;
use app\components\MessagePush;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\GroupPurchaseService;
use yii\db\Exception;

/**
 * 团购订单处理Job
 *
 * 主要功能：
 * - 处理用户加入团购的逻辑
 * - 验证团购资格和限制条件
 * - 更新团购统计信息
 * - 发送相关消息推送
 *
 * <AUTHOR> Name]
 * @since 1.0
 */
class GroupPurchaseJob extends BaseJob
{
    /** @var string 订单号 */
    public $order_no;

    /** @var int 团购ID */
    public $group_purchase_id;

    /** @var int 用户ID */
    public $user_id;

    /** @var int 重试次数限制 */
    protected $attempt = 3;

    /**
     * 执行团购订单处理
     *
     * @param mixed $queue 队列实例
     * @return void
     */
    public function execute($queue)
    {
        $transaction = by::dbMaster()->beginTransaction();

        try {
            // 1. 验证基础数据
            $validationResult = $this->validateBasicData();

            if (!$validationResult['success']) {
                return;
            }

            $leader       = $validationResult['leader'];
            $activityData = $validationResult['activityData'];
            $orderGoods   = $validationResult['orderGoods'];

            // 2. 验证团购资格
            $eligibilityResult = $this->checkGroupPurchaseEligibility($leader, $activityData, $orderGoods);
            if ($eligibilityResult['canJoin']) {
                // 3. 处理团购加入逻辑
                $this->processGroupPurchaseJoin($leader, $orderGoods);
                // 4. 更新团购统计信息
                $this->updateGroupPurchaseStats($leader, $orderGoods, $activityData);
                $transaction->commit();
                // 5. 团长发送消息推送,如果自己参团不推送
                if ($this->user_id != $leader['user_id']) {
                    $this->sendGroupPurchaseNotification($leader);
                }
            } else {
                if ($eligibilityResult['needUpdateOrder']) {
                    // 6 非团购订单，清空订单的团购信息
                    $this->clearOrderGroupInfo();
                    $transaction->commit();
                }
            }

        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->logError($e);
        }
    }

    /**
     * 验证基础数据
     *
     * @return array 验证结果
     * @throws Exception
     */
    private function validateBasicData(): array
    {
        // 获取团长信息
        $leader = byNew::GroupPurchaseModel()->getLeaderInfo($this->group_purchase_id);
        if (!$leader) {
            $this->logDebug('未找到团长信息');
            return ['success' => false];
        }

        // 获取活动详情
        $activityData = byNew::GroupPurchaseActivityModel()->getActivityDetail($leader['activity_id']);
        if (empty($activityData['data']['goods'])) {
            $this->logDebug('活动商品信息不存在');
            return ['success' => false];
        }

        // 获取订单商品信息
        $orderGoods = by::Ogoods()->GetListByOrderNo($this->user_id, $this->order_no);
        if (empty($orderGoods)) {
            $this->logDebug('未找到订单商品信息');
            return ['success' => false];
        }

        return [
                'success'      => true,
                'leader'       => $leader,
                'activityData' => $activityData,
                'orderGoods'   => $orderGoods
        ];
    }

    /**
     * 检查团购资格
     *
     * @param array $leader 团长信息
     * @param array $activityData 活动数据
     * @param array $orderGoods 订单商品
     * @return array 检查结果
     */
    private function checkGroupPurchaseEligibility(array $leader, array $activityData, array $orderGoods): array
    {
        $orderGoodsId = $orderGoods[0]['gid'] ?? 0;

        // 获取商品最大成团人数
        $goodsMaxMembers = array_column($activityData['data']['goods'], 'max_members', 'gid');
        $maxMembers      = $goodsMaxMembers[$orderGoodsId] ?? 0;

        // 获取团成员信息
        $members      = byNew::GroupPurchaseMemberModel()->getGroupMembers($leader['activity_id'], $leader['id']);
        $users        = array_unique(array_column($members, 'user_id'));
        $membersCount = count($users);

        // 获取商品信息
        $goods = Collection::make($activityData['data']['goods'])->where('gid', $orderGoodsId)->first();

        // 检查用户是否已参团
        if (in_array($this->user_id, $users)) {
            $userGoodsNum  = byNew::GroupPurchaseMemberModel()->getUserGoodsSum($this->user_id, $leader['id']);
            $purchaseLimit = $goods['purchase_limit'] ?? 0;
            if ($userGoodsNum >= $purchaseLimit) {
                $this->logDebug('用户已达到最大购买数量限制');
                return ['canJoin' => false, 'needUpdateOrder' => true];
            }
        }
        // 检查是否满团
        if ($membersCount >= $maxMembers) {
            $this->logDebug('团已满员，无法加入');
            return [
                    'canJoin'         => false,
                    'needUpdateOrder' => true
            ];
        }

        return ['canJoin' => true, 'needUpdateOrder' => false];
    }

    /**
     * 处理团购加入逻辑
     *
     * @param array $leader 团长信息
     * @param array $orderGoods 订单商品
     * @return void
     * @throws Exception
     */
    private function processGroupPurchaseJoin(array $leader, array $orderGoods)
    {
        // 获取用户UID
        $uid = by::usersMall()->getInfoByUserId($this->user_id)['uid'] ?? '';
        if (!$uid) {
            throw new \Exception('用户信息不存在');
        }

        $orderGoodsNum = $orderGoods[0]['num'] ?? 0;

        // 验证用户资格是否是老用户
        list($status, $msg, $code) = GroupPurchaseService::getInstance()->verifyQualification($this->user_id);

        // 插入团员信息
        $memberData = [
                'activity_id'       => $leader['activity_id'],
                'group_purchase_id' => $leader['id'],
                'user_id'           => $this->user_id,
                'uid'               => $uid,
                'order_no'          => $this->order_no,
                'items_qty'         => $orderGoodsNum,
                'new_member'        => $code == 1 ? 0 : 1, // 0:老用户 1:新用户
        ];

        CUtil::mSave(byNew::GroupPurchaseMemberModel(), $memberData);
        $this->logDebug('成功添加团员信息');
    }

    /**
     * 更新团购统计信息
     *
     * @param array $leader 团长信息
     * @param array $orderGoods 订单商品
     * @param array $activityData 活动数据
     * @return void
     */
    private function updateGroupPurchaseStats(array $leader, array $orderGoods, array $activityData)
    {
        $orderGoodsId = $orderGoods[0]['gid'] ?? 0;

        // 查询团成员数量
        $memberDistinctCount = byNew::GroupPurchaseMemberModel()->getMemberDistinctCount($this->group_purchase_id);

        // 计算商品统计数据
        $goodsNum        = array_sum(array_column($orderGoods, 'num'));
        $goodsPrice      = array_sum(array_column($orderGoods, 'price'));
        $goodsMaxMembers = byNew::GroupPurchaseActivityGoodsModel()->getInfoByGIdAndAid($orderGoodsId, $leader['activity_id'])['max_members'] ?? 0;

        // 更新团购信息
        $groupData = [
                'id'            => $this->group_purchase_id,
                'total_members' => $memberDistinctCount,
                'total_items'   => new \yii\db\Expression('total_items + ' . $goodsNum),
                'total_price'   => new \yii\db\Expression('total_price + ' . $goodsPrice),
                'status'        => byNew::GroupPurchaseModel()::setStatus($goodsMaxMembers, $memberDistinctCount),
        ];

        CUtil::mSave(byNew::GroupPurchaseModel(), $groupData);

        // 更新商品参团人数统计
        GroupPurchaseService::getInstance()->updateGroupPurchaseGoodsMember(
                (int) $leader['activity_id'],
                (int) $orderGoodsId
        );

        $this->logDebug('团购统计信息更新完成');
    }

    /**
     * 发送团购通知消息
     *
     * @param array $leader 团长信息
     * @return void
     */
    private function sendGroupPurchaseNotification(array $leader)
    {
        $targetUid = $leader['uid'] ?? '';
        if (!$targetUid) {
            $this->logDebug('团长UID为空，跳过消息推送');
            return;
        }

        $pushUrl = 'pagesB/inGroupPurchase/index?group_purchase_id=' . $this->group_purchase_id;

        GroupPurchaseService::getInstance()->sendPush(
                MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_JOIN'],
                [$targetUid],
                $pushUrl
        );

        $this->logDebug('团购加入通知已发送');
    }

    /**
     * 清空订单的团购信息
     *
     * @return void
     * @throws Exception
     */
    private function updateOrderGroupInfo()
    {
        $tbInfo = by::Ouser()::tbName($this->user_id);

        by::dbMaster()->createCommand()->update(
                $tbInfo,
                ['group_purchase_id' => 0],
                ['order_no' => $this->order_no, 'user_id' => $this->user_id]
        )->execute();

        // 清理订单缓存
        by::Ouser()->DelInfoCache($this->user_id,$this->order_no);
        $this->logDebug('订单团购信息已清空');
    }

    /**
     * 记录调试日志
     *
     * @param string $message 日志消息
     * @return void
     */
    private function logDebug(string $message)
    {
        $logData = [
                'order_no'          => $this->order_no,
                'group_purchase_id' => $this->group_purchase_id,
                'user_id'           => $this->user_id,
                'message'           => $message
        ];

        CUtil::debug(json_encode($logData, JSON_UNESCAPED_UNICODE), 'group-purchase.job');
    }

    /**
     * 记录错误日志
     *
     * @param \Exception $e 异常对象
     * @return void
     */
    private function logError(\Exception $e)
    {
        $error = sprintf(
                '%s|%s:%d',
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
        );

        CUtil::debug($error, 'err.group-purchase.job');
    }

    /**
     * 判断是否可以重试
     *
     * @param int $attempt 重试次数
     * @param \Exception $error 异常信息
     * @return bool 是否可以重试
     */
    public function canRetry($attempt, $error): bool
    {
        $params = [
                'order_no'          => $this->order_no,
                'group_purchase_id' => $this->group_purchase_id,
                'user_id'           => $this->user_id,
        ];

        $message = [
                'exception' => get_class($error),
                'message'   => $error->getMessage(),
                'file'      => $error->getFile(),
                'line'      => $error->getLine(),
        ];

        $logMessage = sprintf(
                "重试次数：%d，参数：%s，异常：%s",
                $attempt,
                json_encode($params, JSON_UNESCAPED_UNICODE),
                json_encode($message, JSON_UNESCAPED_UNICODE)
        );

        CUtil::debug($logMessage, 'err.group-purchase.job');

        return parent::canRetry($attempt, $error);
    }
}